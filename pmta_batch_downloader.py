from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException
import os
import time
from datetime import datetime
import re

# 配置下载目录 - 使用当前日期创建文件夹
today = datetime.now().strftime("%Y-%m-%d")
base_download_dir = os.path.join(os.getcwd(), f"pmta_logs_{today}")
if not os.path.exists(base_download_dir):
    os.makedirs(base_download_dir)
print(f"主下载目录: {base_download_dir}")

# 读取服务器列表
def read_server_urls(file_path="pmta_servers.txt"):
    urls = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                # 跳过空行和注释行
                if line and not line.startswith('#'):
                    # 确保URL以/ui/logs结尾
                    if not line.endswith('/ui/logs'):
                        # 如果URL以/结尾，添加ui/logs
                        if line.endswith('/'):
                            line = line + 'ui/logs'
                        # 如果URL不以/结尾，添加/ui/logs
                        else:
                            line = line + '/ui/logs'
                    urls.append(line)
        print(f"从 {file_path} 读取了 {len(urls)} 个服务器URL")
        return urls
    except Exception as e:
        print(f"读取服务器列表时出错: {str(e)}")
        return []

# 从URL中提取服务器IP或域名
def extract_server_name(url):
    match = re.search(r'//([^:/]+)', url)
    if match:
        return match.group(1)
    return "unknown_server"

# 下载单个服务器的日志
def download_server_logs(url, driver, max_files=5):
    server_name = extract_server_name(url)
    print(f"\n===== 处理服务器: {server_name} =====")
    
    # 为每个服务器创建单独的下载目录
    server_download_dir = os.path.join(base_download_dir, server_name)
    if not os.path.exists(server_download_dir):
        os.makedirs(server_download_dir)
    
    # 更新Chrome下载目录
    driver.execute_cdp_cmd('Page.setDownloadBehavior', {
        'behavior': 'allow',
        'downloadPath': server_download_dir
    })
    
    try:
        # 设置页面加载超时
        driver.set_page_load_timeout(30)
        
        # 打开PMTA日志页面
        print(f"正在打开 {url}")
        driver.get(url)
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(5)
        
        # 保存页面截图以便调试
        screenshot_path = os.path.join(server_download_dir, "page_screenshot.png")
        driver.save_screenshot(screenshot_path)
        print(f"页面截图已保存到: {screenshot_path}")
        
        # 查找所有表格
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"找到 {len(tables)} 个表格")
        
        # 遍历表格，查找包含acct或diag的CSV文件
        csv_files_found = 0
        downloaded_files = 0
        
        for table in tables:
            # 检查表格是否包含CSV文件
            if "acct-" in table.text or "diag-" in table.text:
                print("找到包含CSV文件的表格")
                
                # 获取所有行
                rows = table.find_elements(By.TAG_NAME, "tr")
                print(f"表格中有 {len(rows)} 行")
                
                # 跳过表头行
                for i, row in enumerate(rows[1:], 1):
                    try:
                        # 获取文件名
                        columns = row.find_elements(By.TAG_NAME, "td")
                        if len(columns) >= 1:
                            file_name = columns[0].text
                            
                            # 只处理CSV文件
                            if file_name.endswith(".csv") and ("acct-" in file_name or "diag-" in file_name):
                                csv_files_found += 1
                                print(f"[{i}/{len(rows)-1}] 找到CSV文件: {file_name}")
                                
                                # 查找下载链接
                                download_links = row.find_elements(By.LINK_TEXT, "download")
                                if download_links:
                                    print(f"  正在下载: {file_name}")
                                    download_links[0].click()
                                    time.sleep(2)  # 等待下载开始
                                    downloaded_files += 1
                                    
                                    # 限制下载文件数量
                                    if downloaded_files >= max_files:
                                        print(f"已达到最大下载数量 {max_files}")
                                        break
                                else:
                                    print(f"  未找到下载链接: {file_name}")
                    except Exception as e:
                        print(f"处理行时出错: {str(e)}")
                        continue
                
                # 如果已达到最大下载数量，跳出表格循环
                if downloaded_files >= max_files:
                    break
        
        if csv_files_found == 0:
            print("未找到任何CSV文件")
        
        # 等待所有下载完成
        print("等待所有下载完成...")
        time.sleep(5)
        
        # 检查下载的文件
        downloaded_list = os.listdir(server_download_dir)
        # 过滤掉截图文件
        downloaded_list = [f for f in downloaded_list if f.endswith('.csv')]
        print(f"\n下载完成! 共找到 {csv_files_found} 个CSV文件，尝试下载 {downloaded_files} 个文件")
        print(f"下载目录中有 {len(downloaded_list)} 个CSV文件:")
        for file in downloaded_list:
            print(f"  - {file}")
        
        return len(downloaded_list)
    
    except TimeoutException:
        print(f"打开 {url} 超时")
        return 0
    except Exception as e:
        print(f"处理服务器 {server_name} 时出错: {str(e)}")
        return 0

# 主函数
def main():
    # 读取服务器列表
    server_urls = read_server_urls()
    if not server_urls:
        print("没有找到有效的服务器URL，请检查pmta_servers.txt文件")
        return
    
    # 配置Chrome选项
    chrome_options = Options()
    prefs = {
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": False
    }
    chrome_options.add_experimental_option("prefs", prefs)
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    try:
        # 初始化WebDriver
        print("正在启动Chrome浏览器...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 处理每个服务器
        total_servers = len(server_urls)
        successful_servers = 0
        total_files_downloaded = 0
        
        for i, url in enumerate(server_urls, 1):
            print(f"\n处理服务器 {i}/{total_servers}: {url}")
            files_downloaded = download_server_logs(url, driver, max_files=5)
            total_files_downloaded += files_downloaded
            
            if files_downloaded > 0:
                successful_servers += 1
        
        # 显示总结
        print("\n===== 下载任务完成 =====")
        print(f"总共处理了 {total_servers} 个服务器")
        print(f"成功从 {successful_servers} 个服务器下载了文件")
        print(f"总共下载了 {total_files_downloaded} 个文件")
        print(f"所有文件已保存到: {base_download_dir}")
    
    except Exception as e:
        print(f"执行过程中出错: {str(e)}")
    
    finally:
        # 关闭浏览器
        try:
            print("\n关闭浏览器")
            driver.quit()
        except:
            print("关闭浏览器时出错")
        
        print("\n程序执行完毕")
        input("按Enter键退出...")

if __name__ == "__main__":
    main()